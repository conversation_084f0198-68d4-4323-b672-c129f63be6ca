# 部署指南

## 快速部署

### 1. 使用 Docker Compose (推荐)

```bash
# 克隆项目
git clone <repository-url>
cd pandoc-fastapi

# 启动服务
cd docker
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 2. 本地开发部署

```bash
# 安装 Pandoc
sudo apt-get install pandoc texlive-latex-base texlive-latex-recommended

# 安装 Python 依赖
uv sync

# 配置环境
cp .env.example .env

# 启动服务
uv run python run.py
```

## 验证部署

1. 访问健康检查：http://localhost:8000/health
2. 访问 API 文档：http://localhost:8000/docs
3. 运行示例脚本：`python examples/simple_conversion.py`

## 生产环境配置

### 环境变量

```env
# 生产环境配置
DEBUG=false
SECRET_KEY=your-very-secure-secret-key
API_HOST=0.0.0.0
API_PORT=8000

# 数据库（可选择使用 PostgreSQL）
DATABASE_URL=postgresql://user:password@localhost/pandoc_api

# 文件大小限制
MAX_FILE_SIZE=104857600  # 100MB

# Pandoc 超时
PANDOC_TIMEOUT=300  # 5分钟
```

### Nginx 反向代理

```nginx
server {
    listen 80;
    server_name your-domain.com;

    client_max_body_size 100M;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 文件清理

建议设置定期清理任务：

```bash
# 添加到 crontab
# 每天凌晨清理超过 24 小时的文件
0 0 * * * find /app/storage/uploads -type f -mtime +1 -delete
0 0 * * * find /app/storage/outputs -type f -mtime +1 -delete
```

## 监控和日志

### 健康检查

- 端点：`GET /health`
- 返回：`{"status": "healthy"}`

### 日志配置

在生产环境中，建议配置结构化日志：

```python
import logging
import sys

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
```

## 扩展和自定义

### 添加新的模板

1. 通过 API 上传：
```bash
curl -X POST "http://localhost:8000/api/v1/templates/" \
  -F "file=@template.docx" \
  -F "name=company_template" \
  -F "display_name=Company Template" \
  -F "resource_type=docx_template"
```

2. 直接放置文件：
```bash
# 将文件放到对应目录
cp template.docx templates/docx/
# 然后通过 API 注册
```

### 自定义 Pandoc 选项

在转换请求中使用 `options` 字段：

```json
{
  "to_format": "pdf",
  "options": {
    "pdf-engine": "xelatex",
    "geometry": "margin=1in",
    "fontsize": "12pt"
  }
}
```

## 故障排除

### 常见问题

1. **Pandoc 未找到**
   ```bash
   # 检查 Pandoc 是否安装
   which pandoc
   pandoc --version
   ```

2. **权限问题**
   ```bash
   # 确保目录权限正确
   chmod -R 755 storage/
   chmod -R 755 templates/
   ```

3. **内存不足**
   - 增加 Docker 内存限制
   - 减少 `MAX_FILE_SIZE`
   - 设置 `PANDOC_TIMEOUT`

4. **文件格式不支持**
   ```bash
   # 检查 Pandoc 支持的格式
   pandoc --list-input-formats
   pandoc --list-output-formats
   ```

### 调试模式

```bash
# 启用调试模式
export DEBUG=true
uv run python run.py
```

## 安全注意事项

1. **更改默认密钥**：生产环境必须设置强密钥
2. **文件上传限制**：设置合理的文件大小限制
3. **CORS 配置**：限制允许的域名
4. **输入验证**：Pandoc 参数需要验证
5. **文件清理**：定期清理临时文件
6. **访问控制**：考虑添加认证机制
