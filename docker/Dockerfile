FROM pandoc/core:latest 
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Set working directory
WORKDIR /app

# Copy project files
COPY pyproject.toml .python-version uv.lock ./

# Install Python dependencies
RUN uv sync --locked

# Copy application code
COPY app/ ./app/
COPY templates/ ./templates/

# Create necessary directories
RUN mkdir -p storage/uploads storage/outputs

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
