version: '3.8'

services:
  pandoc-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/pandoc_api.db
      - UPLOAD_DIR=storage/uploads
      - OUTPUT_DIR=storage/outputs
      - TEMPLATE_DIR=templates
      - ADDITIONAL_TEMPLATE_DIRS=/app/custom_templates
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - DEBUG=false
      - PANDOC_TIMEOUT=300
      - MAX_FILE_SIZE=104857600  # 100MB
      - SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - pandoc_data:/app/data
      - pandoc_storage:/app/storage
      - pandoc_templates:/app/templates
      - pandoc_custom_templates:/app/custom_templates
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  pandoc_data:
    driver: local
  pandoc_storage:
    driver: local
  pandoc_templates:
    driver: local
  pandoc_custom_templates:
    driver: local
