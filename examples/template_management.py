#!/usr/bin/env python3
"""
Template Management Example

This example demonstrates how to work with the new template system:
- Upload templates with metadata
- Search templates
- Reload templates from filesystem
- Use additional template directories
"""

import json
import requests
from pathlib import Path


def main():
    base_url = "http://localhost:8000"

    print("🔧 Template Management Example")
    print("=" * 50)

    try:
        # Example 1: Upload a template with metadata
        print("\n📤 Uploading template with metadata...")

        # Create a dummy template file
        template_content = b"Dummy DOCX template content"

        # Define metadata
        metadata = {
            "version": "1.0.0",
            "author": "Example Author",
            "tags": ["example", "demo"],
            "supported_formats": ["docx", "pdf"],
            "custom_fields": {"category": "demo", "complexity": "simple"},
        }

        files = {
            "file": (
                "demo_template.docx",
                template_content,
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            )
        }

        data = {
            "name": "demo_template",
            "display_name": "Demo Template",
            "resource_type": "docx_template",
            "description": "A demo template for testing",
            "metadata": json.dumps(metadata),
        }

        response = requests.post(
            f"{base_url}/api/v1/templates/", files=files, data=data
        )
        if response.status_code == 200:
            template_data = response.json()
            print(f"✅ Template uploaded: {template_data['name']}")
            print(f"   Display name: {template_data['display_name']}")
            print(f"   Version: {template_data['metadata']['version']}")
            print(f"   Tags: {template_data['metadata']['tags']}")
        else:
            print(f"❌ Upload failed: {response.text}")

        # Example 2: Search templates
        print("\n🔍 Searching templates...")
        search_data = {"query": "demo", "tags": ["example"]}

        response = requests.post(
            f"{base_url}/api/v1/templates/search",
            headers={"Content-Type": "application/json"},
            data=json.dumps(search_data),
        )

        if response.status_code == 200:
            search_results = response.json()
            print(f"✅ Found {search_results['total']} templates")
            for template in search_results["resources"]:
                print(f"   - {template['name']}: {template['display_name']}")
                if template.get("metadata", {}).get("tags"):
                    print(f"     Tags: {template['metadata']['tags']}")

        # Example 3: List built-in templates
        print("\n📁 Listing built-in templates...")
        response = requests.get(f"{base_url}/api/v1/templates/builtin/")
        if response.status_code == 200:
            builtin_data = response.json()
            print(f"✅ Found {builtin_data['total']} built-in templates")
            for template in builtin_data["templates"]:
                print(f"   - {template['name']}: {template['display_name']}")

        # Example 4: Reload templates
        print("\n🔄 Reloading templates...")
        response = requests.post(f"{base_url}/api/v1/templates/reload")
        if response.status_code == 200:
            reload_data = response.json()
            print("✅ Templates reloaded successfully")
            print(f"   Built-in loaded: {reload_data['builtin_loaded']}")
            print(f"   User loaded: {reload_data['user_loaded']}")
            print(f"   Additional loaded: {reload_data['additional_loaded']}")
            print(f"   Errors: {reload_data['errors']}")

        # Example 5: List all templates with pagination
        print("\n📋 Listing all templates...")
        response = requests.get(f"{base_url}/api/v1/templates/?page=1&page_size=10")
        if response.status_code == 200:
            templates_data = response.json()
            print(f"✅ Found {templates_data['total']} total templates")
            print(
                f"   Showing page {templates_data['page']} of {templates_data['page_size']} per page"
            )

            for template in templates_data["resources"]:
                print(f"   - {template['name']}: {template['display_name']}")
                print(f"     Type: {template['resource_type']}")
                print(f"     Built-in: {template['is_builtin']}")
                if template.get("metadata", {}).get("version"):
                    print(f"     Version: {template['metadata']['version']}")

    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the API server.")
        print("   Make sure the server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ An error occurred: {e}")

    print("\n🎉 Template management demo completed!")
    print("\nNext steps:")
    print("1. Create template files with .metadata files in templates/builtin/")
    print("2. Set ADDITIONAL_TEMPLATE_DIRS to add custom template directories")
    print("3. Use the search API to find templates by tags and content")
    print("4. Explore the template metadata system for rich template information")


if __name__ == "__main__":
    main()
