#!/usr/bin/env python3
"""
Pandoc FastAPI Service Runner

This script provides a convenient way to run the Pandoc FastAPI service
with proper configuration and error handling.
"""

import os
import sys
from pathlib import Path

import uvicorn

# Add the app directory to Python path
app_dir = Path(__file__).parent
sys.path.insert(0, str(app_dir))

from app.core.config import settings


def main():
    """Main entry point"""

    # Ensure required directories exist
    settings.upload_dir.mkdir(parents=True, exist_ok=True)
    settings.output_dir.mkdir(parents=True, exist_ok=True)
    settings.template_dir.mkdir(parents=True, exist_ok=True)

    # Check if Pandoc is available
    import shutil

    if not shutil.which("pandoc"):
        print("Warning: Pandoc not found in PATH. Please install Pandoc.")
        print("Ubuntu/Debian: sudo apt-get install pandoc")
        print("macOS: brew install pandoc")
        print("Windows: Download from https://pandoc.org/installing.html")

    print(f"Starting Pandoc FastAPI Service...")
    print(f"Host: {settings.api_host}")
    print(f"Port: {settings.api_port}")
    print(f"Debug: {settings.debug}")
    print(f"Database: {settings.database_url}")
    print(f"Upload dir: {settings.upload_dir}")
    print(f"Output dir: {settings.output_dir}")
    print(f"Template dir: {settings.template_dir}")
    print()
    print("API Documentation:")
    print(f"  Swagger UI: http://{settings.api_host}:{settings.api_port}/docs")
    print(f"  ReDoc: http://{settings.api_host}:{settings.api_port}/redoc")
    print()

    # Run the server
    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug",
    )


if __name__ == "__main__":
    main()
