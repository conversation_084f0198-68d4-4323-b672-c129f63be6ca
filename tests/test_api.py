"""API tests"""

import io
import json
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.core.database import get_db, Base


# Test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)


@pytest.fixture(scope="module")
def setup_database():
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def test_root_endpoint():
    """Test root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data


def test_health_check():
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"


def test_get_supported_formats():
    """Test get supported formats endpoint"""
    response = client.get("/api/v1/convert/formats")
    assert response.status_code == 200
    data = response.json()
    assert "input_formats" in data
    assert "output_formats" in data
    assert isinstance(data["input_formats"], list)
    assert isinstance(data["output_formats"], list)


def test_get_resource_types():
    """Test get resource types endpoint"""
    response = client.get("/api/v1/templates/types/")
    assert response.status_code == 200
    data = response.json()
    assert "resource_types" in data
    assert isinstance(data["resource_types"], list)


def test_list_templates_empty(setup_database):
    """Test list templates when empty"""
    response = client.get("/api/v1/templates/")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 0
    assert data["resources"] == []


def test_upload_template(setup_database):
    """Test template upload"""
    # Create a dummy file
    file_content = b"dummy template content"
    files = {
        "file": (
            "test_template.docx",
            io.BytesIO(file_content),
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        )
    }

    data = {
        "name": "test_template",
        "display_name": "Test Template",
        "resource_type": "docx_template",
        "description": "A test template",
    }

    response = client.post("/api/v1/templates/", files=files, data=data)
    assert response.status_code == 200

    template_data = response.json()
    assert template_data["name"] == "test_template"
    assert template_data["display_name"] == "Test Template"
    assert template_data["resource_type"] == "docx_template"
    assert template_data["is_builtin"] == False


def test_upload_template_with_metadata(setup_database):
    """Test template upload with metadata"""
    # Create a dummy file
    file_content = b"dummy template content with metadata"
    files = {
        "file": (
            "test_template_meta.docx",
            io.BytesIO(file_content),
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        )
    }

    metadata = {
        "version": "2.0.0",
        "author": "Test Author",
        "tags": ["business", "formal"],
        "supported_formats": ["docx", "pdf"],
    }

    data = {
        "name": "test_template_meta",
        "display_name": "Test Template with Metadata",
        "resource_type": "docx_template",
        "description": "A test template with metadata",
        "metadata": json.dumps(metadata),
    }

    response = client.post("/api/v1/templates/", files=files, data=data)
    assert response.status_code == 200

    template_data = response.json()
    assert template_data["name"] == "test_template_meta"
    assert template_data["metadata"]["version"] == "2.0.0"
    assert template_data["metadata"]["author"] == "Test Author"
    assert "business" in template_data["metadata"]["tags"]


def test_template_reload(setup_database):
    """Test template reload endpoint"""
    response = client.post("/api/v1/templates/reload")
    assert response.status_code == 200

    reload_data = response.json()
    assert reload_data["success"] == True
    assert "builtin_loaded" in reload_data
    assert "user_loaded" in reload_data
    assert "errors" in reload_data


def test_list_builtin_templates(setup_database):
    """Test list built-in templates"""
    response = client.get("/api/v1/templates/builtin/")
    assert response.status_code == 200

    data = response.json()
    assert "templates" in data
    assert "total" in data
    assert isinstance(data["templates"], list)


def test_convert_document_simple():
    """Test simple document conversion"""
    # Create a simple markdown file
    markdown_content = b"# Test Document\n\nThis is a test."
    files = {"file": ("test.md", io.BytesIO(markdown_content), "text/markdown")}

    data = {"to_format": "html"}

    response = client.post("/api/v1/convert/", files=files, data=data)

    # Note: This test might fail if Pandoc is not installed in test environment
    # In a real test environment, you would mock the PandocRunner
    if response.status_code == 200:
        conversion_data = response.json()
        assert conversion_data["success"] is True
        assert "output_files" in conversion_data
    else:
        # Expected if Pandoc is not available in test environment
        assert response.status_code in [500, 422]
